<?php
session_start();

if (!isset($_SESSION['nama'])) {
    header("Location: ../index.php");
    exit;
}

if ($_SESSION['level'] != "client") {
    echo "<script>alert('Anda tidak memiliki akses ke halaman ini!'); window.location='../index.php';</script>";
    exit;
}

require '../koneksi.php';

// Ambil daftar tugas proyek untuk dropdown
$query_tugas = "SELECT id, nama_kegiatan, status FROM tugas_proyek ORDER BY tgl DESC";
$result_tugas = mysqli_query($koneksi, $query_tugas);

// Ambil data revisi yang sudah diajukan oleh client ini
$client_id = $_SESSION['id_petugas'];
$query_revisi = "SELECT rr.*, tp.nama_kegiatan, p.nama_petugas as handler_name
                 FROM revision_requests rr
                 LEFT JOIN tugas_proyek tp ON rr.tugas_id = tp.id
                 LEFT JOIN petugas p ON rr.handled_by = p.id_petugas
                 WHERE rr.client_id = '$client_id'
                 ORDER BY rr.created_at DESC";
$result_revisi = mysqli_query($koneksi, $query_revisi);

// Handle messages
$success_message = isset($_SESSION['success_message']) ? $_SESSION['success_message'] : '';
$error_messages = isset($_SESSION['error_messages']) ? $_SESSION['error_messages'] : array();
$form_data = isset($_SESSION['form_data']) ? $_SESSION['form_data'] : array();

// Clear session messages
unset($_SESSION['success_message']);
unset($_SESSION['error_messages']);
unset($_SESSION['form_data']);
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>Ajukan Revisi - Client Dashboard</title>

    <!-- Custom fonts for this template-->
    <link href="../tmp/vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">
    <link href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i" rel="stylesheet">

    <!-- Custom styles for this template-->
    <link href="../tmp/css/sb-admin-2.min.css" rel="stylesheet">
</head>

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">

            <!-- Sidebar - Brand -->
            <a class="sidebar-brand d-flex align-items-center justify-content-center" href="client.php">
                <div class="sidebar-brand-icon rotate-n-15">
                    <i class="fas fa-keyboard"></i>
                </div>
                <div class="sidebar-brand-text mx-3">antosa</div>
            </a>

            <!-- Divider -->
            <hr class="sidebar-divider my-0">

            <!-- Nav Item - Dashboard -->
            <li class="nav-item">
                <a class="nav-link" href="client.php">
                    <i class="fas fa-fw fa-tachometer-alt"></i>
                    <span>Dashboard</span></a>         
            </li>
          
            <!-- Divider -->
            <hr class="sidebar-divider">

            <!-- Heading -->
            <div class="sidebar-heading">
                Interface
            </div>
            
             <li class="nav-item">
                <a class="nav-link" href="lihat_progress.php">
                    <i class="fas fa-fw fa-chart-line"></i>
                    <span>Lihat Progress</span></a>
            </li>

             <li class="nav-item">
                <a class="nav-link" href="file_management.php">
                    <i class="fas fa-fw fa-folder"></i>
                    <span>File Management</span></a>
            </li>

             <li class="nav-item">
                <a class="nav-link" href="upload_brief.php">
                    <i class="fas fa-fw fa-cloud-upload-alt"></i>
                    <span>Upload Brief & Referensi</span></a>
            </li>

             <li class="nav-item active">
                <a class="nav-link" href="ajukan_revisi.php">
                    <i class="fas fa-fw fa-edit"></i>
                    <span>Ajukan Revisi</span></a>
            </li>

            <!-- Divider -->
            <hr class="sidebar-divider d-none d-md-block">

             <li class="nav-item">
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Keluar</span></a>
            </li>
            
            <!-- Sidebar Toggler (Sidebar) -->
            <div class="text-center d-none d-md-inline">
                <button class="rounded-circle border-0" id="sidebarToggle"></button>
            </div>

        </ul>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">

                    <!-- Sidebar Toggle (Topbar) -->
                    <button id="sidebarToggleTop" class="btn btn-link d-md-none rounded-circle mr-3">
                        <i class="fa fa-bars"></i>
                    </button>

                    <h1>Ajukan Revisi</h1>

                </nav>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">

                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">Ajukan Revisi Desain</h1>
                    </div>

                    <!-- Messages -->
                    <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($error_messages)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle"></i>
                        <ul class="mb-0">
                            <?php foreach ($error_messages as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <?php endif; ?>

                    <!-- Form Ajukan Revisi -->
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">Form Pengajuan Revisi</h6>
                                </div>
                                <div class="card-body">
                                    <form action="proses_ajukan_revisi.php" method="POST" enctype="multipart/form-data">
                                        <div class="form-group">
                                            <label for="tugas_id">Pilih Tugas/Proyek <span class="text-danger">*</span></label>
                                            <select class="form-control" id="tugas_id" name="tugas_id" required>
                                                <option value="">-- Pilih Tugas --</option>
                                                <?php
                                                mysqli_data_seek($result_tugas, 0); // Reset pointer
                                                while($tugas = mysqli_fetch_array($result_tugas)):
                                                    $selected = (isset($form_data['tugas_id']) && $form_data['tugas_id'] == $tugas['id']) ? 'selected' : '';
                                                ?>
                                                <option value="<?php echo $tugas['id']; ?>" <?php echo $selected; ?>>
                                                    <?php echo htmlspecialchars($tugas['nama_kegiatan']); ?>
                                                    (<?php echo ucfirst($tugas['status']); ?>)
                                                </option>
                                                <?php endwhile; ?>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label for="judul_revisi">Judul Revisi <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="judul_revisi" name="judul_revisi"
                                                   placeholder="Masukkan judul singkat revisi" required maxlength="200"
                                                   value="<?php echo isset($form_data['judul_revisi']) ? htmlspecialchars($form_data['judul_revisi']) : ''; ?>">
                                        </div>

                                        <div class="form-group">
                                            <label for="deskripsi_revisi">Deskripsi Revisi <span class="text-danger">*</span></label>
                                            <textarea class="form-control" id="deskripsi_revisi" name="deskripsi_revisi"
                                                      rows="5" placeholder="Jelaskan detail revisi yang diinginkan..." required><?php echo isset($form_data['deskripsi_revisi']) ? htmlspecialchars($form_data['deskripsi_revisi']) : ''; ?></textarea>
                                        </div>

                                        <div class="form-group">
                                            <label for="prioritas">Tingkat Prioritas</label>
                                            <select class="form-control" id="prioritas" name="prioritas">
                                                <?php
                                                $selected_prioritas = isset($form_data['prioritas']) ? $form_data['prioritas'] : 'sedang';
                                                $prioritas_options = array('rendah' => 'Rendah', 'sedang' => 'Sedang', 'tinggi' => 'Tinggi', 'urgent' => 'Urgent');
                                                foreach ($prioritas_options as $value => $label):
                                                    $selected = ($selected_prioritas == $value) ? 'selected' : '';
                                                ?>
                                                <option value="<?php echo $value; ?>" <?php echo $selected; ?>><?php echo $label; ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label for="file_pendukung">File Pendukung (Opsional)</label>
                                            <input type="file" class="form-control-file" id="file_pendukung" name="file_pendukung"
                                                   accept=".jpg,.jpeg,.png,.pdf,.doc,.docx">
                                            <small class="form-text text-muted">
                                                Format yang didukung: JPG, PNG, PDF, DOC, DOCX. Maksimal 5MB.
                                            </small>
                                        </div>

                                        <div class="form-group">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-paper-plane"></i> Ajukan Revisi
                                            </button>
                                            <a href="client.php" class="btn btn-secondary">
                                                <i class="fas fa-arrow-left"></i> Kembali
                                            </a>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Riwayat Revisi -->
                        <div class="col-lg-4">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">Riwayat Revisi Anda</h6>
                                </div>
                                <div class="card-body">
                                    <?php if (mysqli_num_rows($result_revisi) > 0): ?>
                                        <?php while($revisi = mysqli_fetch_array($result_revisi)): ?>
                                        <div class="card mb-3">
                                            <div class="card-body p-3">
                                                <h6 class="card-title"><?php echo htmlspecialchars($revisi['judul_revisi']); ?></h6>
                                                <p class="card-text small text-muted">
                                                    Tugas: <?php echo htmlspecialchars($revisi['nama_kegiatan']); ?>
                                                </p>
                                                <p class="card-text small">
                                                    <?php echo htmlspecialchars(substr($revisi['deskripsi_revisi'], 0, 100)); ?>
                                                    <?php if (strlen($revisi['deskripsi_revisi']) > 100) echo '...'; ?>
                                                </p>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <small class="text-muted">
                                                        <?php echo date('d M Y', strtotime($revisi['created_at'])); ?>
                                                    </small>
                                                    <span class="badge badge-<?php 
                                                        echo $revisi['status_revisi'] == 'pending' ? 'warning' : 
                                                            ($revisi['status_revisi'] == 'diterima' ? 'success' : 
                                                            ($revisi['status_revisi'] == 'ditolak' ? 'danger' : 'info')); 
                                                    ?>">
                                                        <?php echo ucfirst($revisi['status_revisi']); ?>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <p class="text-muted text-center">Belum ada revisi yang diajukan.</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <footer class="sticky-footer bg-white">
                <div class="container my-auto">
                    <div class="copyright text-center my-auto">
                        <span>Copyright &copy;FOKUS UKK!!</span>
                    </div>
                </div>
            </footer>
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Bootstrap core JavaScript -->
    <script src="../tmp/vendor/jquery/jquery.min.js"></script>
    <script src="../tmp/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>

    <!-- Core plugin JavaScript -->
    <script src="../tmp/vendor/jquery-easing/jquery.easing.min.js"></script>

    <!-- Custom scripts for all pages -->
    <script src="../tmp/js/sb-admin-2.min.js"></script>

</body>
</html>
