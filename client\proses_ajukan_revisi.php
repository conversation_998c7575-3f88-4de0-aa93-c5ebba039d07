<?php
session_start();

if (!isset($_SESSION['nama'])) {
    header("Location: ../index.php");
    exit;
}

if ($_SESSION['level'] != "client") {
    echo "<script>alert('Anda tidak memiliki akses ke halaman ini!'); window.location='../index.php';</script>";
    exit;
}

require '../koneksi.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Ambil data dari form
    $tugas_id = mysqli_real_escape_string($koneksi, $_POST['tugas_id']);
    $judul_revisi = mysqli_real_escape_string($koneksi, $_POST['judul_revisi']);
    $deskripsi_revisi = mysqli_real_escape_string($koneksi, $_POST['deskripsi_revisi']);
    $prioritas = mysqli_real_escape_string($koneksi, $_POST['prioritas']);
    $client_id = $_SESSION['id_petugas'];
    
    // Validasi input
    $errors = array();
    
    if (empty($tugas_id)) {
        $errors[] = "Tugas harus dipilih";
    }
    
    if (empty($judul_revisi)) {
        $errors[] = "Judul revisi harus diisi";
    }
    
    if (empty($deskripsi_revisi)) {
        $errors[] = "Deskripsi revisi harus diisi";
    }
    
    // Validasi tugas exists
    $check_tugas = "SELECT id FROM tugas_proyek WHERE id = '$tugas_id'";
    $result_check = mysqli_query($koneksi, $check_tugas);
    if (mysqli_num_rows($result_check) == 0) {
        $errors[] = "Tugas yang dipilih tidak valid";
    }
    
    // Handle file upload jika ada
    $file_name = null;
    if (isset($_FILES['file_pendukung']) && $_FILES['file_pendukung']['error'] == 0) {
        $upload_dir = '../uploads/revisi/';
        
        // Buat direktori jika belum ada
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }
        
        $file_tmp = $_FILES['file_pendukung']['tmp_name'];
        $file_original_name = $_FILES['file_pendukung']['name'];
        $file_size = $_FILES['file_pendukung']['size'];
        $file_ext = strtolower(pathinfo($file_original_name, PATHINFO_EXTENSION));
        
        // Validasi file
        $allowed_extensions = array('jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx');
        $max_file_size = 5 * 1024 * 1024; // 5MB
        
        if (!in_array($file_ext, $allowed_extensions)) {
            $errors[] = "Format file tidak didukung. Gunakan: " . implode(', ', $allowed_extensions);
        }
        
        if ($file_size > $max_file_size) {
            $errors[] = "Ukuran file terlalu besar. Maksimal 5MB";
        }
        
        if (empty($errors)) {
            // Generate unique filename
            $file_name = 'revisi_' . $client_id . '_' . time() . '.' . $file_ext;
            $file_path = $upload_dir . $file_name;
            
            if (!move_uploaded_file($file_tmp, $file_path)) {
                $errors[] = "Gagal mengupload file";
                $file_name = null;
            }
        }
    }
    
    // Jika tidak ada error, simpan ke database
    if (empty($errors)) {
        $query = "INSERT INTO revision_requests (tugas_id, client_id, judul_revisi, deskripsi_revisi, prioritas, file_pendukung) 
                  VALUES ('$tugas_id', '$client_id', '$judul_revisi', '$deskripsi_revisi', '$prioritas', " . 
                  ($file_name ? "'$file_name'" : "NULL") . ")";
        
        if (mysqli_query($koneksi, $query)) {
            $_SESSION['success_message'] = "Revisi berhasil diajukan! Tim proyek akan segera meninjau permintaan Anda.";
            header("Location: ajukan_revisi.php");
            exit;
        } else {
            $errors[] = "Gagal menyimpan data: " . mysqli_error($koneksi);
            
            // Hapus file jika ada error database
            if ($file_name && file_exists($upload_dir . $file_name)) {
                unlink($upload_dir . $file_name);
            }
        }
    }
    
    // Jika ada error, simpan ke session dan redirect kembali
    if (!empty($errors)) {
        $_SESSION['error_messages'] = $errors;
        $_SESSION['form_data'] = $_POST; // Simpan data form untuk ditampilkan kembali
        header("Location: ajukan_revisi.php");
        exit;
    }
    
} else {
    // Jika bukan POST request, redirect ke form
    header("Location: ajukan_revisi.php");
    exit;
}
?>
