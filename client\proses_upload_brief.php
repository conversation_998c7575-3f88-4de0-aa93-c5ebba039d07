<?php
session_start();

if (!isset($_SESSION['nama'])) {
    header("Location: ../index.php");
    exit;
}

if ($_SESSION['level'] != "client") {
    echo "<script>alert('Anda tidak memiliki akses ke halaman ini!'); window.location='../index.php';</script>";
    exit;
}

require '../koneksi.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Sanitize input
    $kategori = mysqli_real_escape_string($koneksi, trim($_POST['kategori']));
    $deskripsi = mysqli_real_escape_string($koneksi, trim($_POST['deskripsi']));
    $uploaded_by = $_SESSION['id_petugas'] ?? 0;

    // Validate input
    if (empty($kategori) || empty($deskripsi)) {
        echo "<script>alert('Kategori dan deskripsi harus diisi!'); window.history.back();</script>";
        exit;
    }

    if (strlen($deskripsi) > 500) {
        echo "<script>alert('Deskripsi maksimal 500 karakter!'); window.history.back();</script>";
        exit;
    }

    // Validate category
    $allowed_categories = ['brief', 'referensi', 'dokumen'];
    if (!in_array($kategori, $allowed_categories)) {
        echo "<script>alert('Kategori tidak valid!'); window.history.back();</script>";
        exit;
    }

    // File validation
    if (!isset($_FILES['file_dokumen']) || $_FILES['file_dokumen']['error'] !== UPLOAD_ERR_OK) {
        echo "<script>alert('Error dalam upload file!'); window.history.back();</script>";
        exit;
    }

    $file = $_FILES['file_dokumen'];

    // Security checks
    $maxFileSize = 25 * 1024 * 1024; // 25MB
    $allowedExt = ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif'];
    $allowedMimeTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'image/jpeg', 'image/jpg', 'image/png', 'image/gif'
    ];

    // Check file size
    if ($file['size'] > $maxFileSize) {
        echo "<script>alert('Ukuran file terlalu besar. Maksimal 25MB.'); window.history.back();</script>";
        exit;
    }

    // Check file extension
    $originalName = $file['name'];
    $fileExt = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
    
    if (!in_array($fileExt, $allowedExt)) {
        echo "<script>alert('Format file tidak didukung. Gunakan: " . implode(', ', $allowedExt) . "'); window.history.back();</script>";
        exit;
    }

    // Check MIME type
    if (!in_array($file['type'], $allowedMimeTypes)) {
        echo "<script>alert('Tipe file tidak valid!'); window.history.back();</script>";
        exit;
    }

    // Additional security checks for images
    if (in_array($fileExt, ['jpg', 'jpeg', 'png', 'gif'])) {
        $imageInfo = getimagesize($file['tmp_name']);
        if ($imageInfo === false) {
            echo "<script>alert('File gambar tidak valid!'); window.history.back();</script>";
            exit;
        }
    }

    // Generate secure filename
    $targetDir = "../file_proyek/";
    $fileHash = hash('sha256', $originalName . time() . $uploaded_by);
    $namaUnik = substr($fileHash, 0, 16) . "_" . preg_replace("/[^a-zA-Z0-9.\-_]/", "_", $originalName);
    $targetFile = $targetDir . $namaUnik;

    // Create directory if not exists with proper permissions
    if (!is_dir($targetDir)) {
        if (!mkdir($targetDir, 0755, true)) {
            echo "<script>alert('Gagal membuat direktori upload!'); window.history.back();</script>";
            exit;
        }
    }

    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $targetFile)) {
        // Insert into database with category information
        $query = "INSERT INTO file_gambar (deskripsi, gambar, uploaded_by, file_size, file_type, kategori_file, original_filename, created_at)
                  VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";

        $stmt = mysqli_prepare($koneksi, $query);

        if ($stmt) {
            mysqli_stmt_bind_param($stmt, "ssissss", $deskripsi, $namaUnik, $uploaded_by, $file['size'], $fileExt, $kategori, $originalName);

            if (mysqli_stmt_execute($stmt)) {
                $new_file_id = mysqli_insert_id($koneksi);

                // Log the upload activity
                $log_query = "INSERT INTO upload_logs (file_id, uploaded_by, kategori, original_filename, file_size, upload_time) 
                             VALUES (?, ?, ?, ?, ?, NOW())";
                $log_stmt = mysqli_prepare($koneksi, $log_query);
                
                if ($log_stmt) {
                    mysqli_stmt_bind_param($log_stmt, "iissi", $new_file_id, $uploaded_by, $kategori, $originalName, $file['size']);
                    mysqli_stmt_execute($log_stmt);
                    mysqli_stmt_close($log_stmt);
                }

                echo "<script>
                    alert('Dokumen berhasil diupload!\\n\\nKategori: " . ucfirst($kategori) . "\\nFile: " . $originalName . "\\nUkuran: " . formatFileSize($file['size']) . "');
                    window.location.href='upload_brief.php';
                </script>";
            } else {
                // Delete uploaded file if database insert fails
                unlink($targetFile);
                echo "<script>alert('Gagal menyimpan ke database: " . mysqli_error($koneksi) . "'); window.history.back();</script>";
            }

            mysqli_stmt_close($stmt);
        } else {
            // Delete uploaded file if prepare fails
            unlink($targetFile);
            echo "<script>alert('Gagal menyiapkan query database!'); window.history.back();</script>";
        }
    } else {
        echo "<script>alert('Gagal mengupload file!'); window.history.back();</script>";
    }
} else {
    echo "<script>alert('Metode request tidak valid!'); window.location.href='upload_brief.php';</script>";
}

function formatFileSize($bytes) {
    if ($bytes == 0) return '0 Bytes';
    $k = 1024;
    $sizes = array('Bytes', 'KB', 'MB', 'GB');
    $i = floor(log($bytes) / log($k));
    return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
}
?>

<script>
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
</script>
