<?php
session_start();

if (!isset($_SESSION['nama'])) {
    header("Location: ../index.php");
    exit;
}

if ($_SESSION['level'] != "client") {
    echo "<script>alert('Anda tidak memiliki akses ke halaman ini!'); window.location='../index.php';</script>";
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>Upload Brief & Referensi - Client</title>

    <!-- Custom fonts for this template-->
    <link href="../tmp/vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">
    <link href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i" rel="stylesheet">

    <!-- Custom styles for this template-->
    <link href="../tmp/css/sb-admin-2.min.css" rel="stylesheet">

    <style>
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background-color: #f8f9fc;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #4e73df;
            background-color: #f0f3ff;
        }
        .upload-area.dragover {
            border-color: #4e73df;
            background-color: #e3f2fd;
        }
        .file-preview {
            max-width: 100%;
            max-height: 200px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .file-item {
            background: white;
            border: 1px solid #e3e6f0;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        .file-item:hover {
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }
    </style>
</head>

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">

            <!-- Sidebar - Brand -->
            <a class="sidebar-brand d-flex align-items-center justify-content-center" href="client.php">
                <div class="sidebar-brand-icon rotate-n-15">
                    <i class="fas fa-keyboard"></i>
                </div>
                <div class="sidebar-brand-text mx-3">Antosa Client</div>
            </a>

            <!-- Divider -->
            <hr class="sidebar-divider my-0">

            <!-- Nav Item - Dashboard -->
            <li class="nav-item">
                <a class="nav-link" href="client.php">
                    <i class="fas fa-fw fa-tachometer-alt"></i>
                    <span>Dashboard</span></a>
            </li>

            <!-- Divider -->
            <hr class="sidebar-divider">

            <!-- Heading -->
            <div class="sidebar-heading">
                Interface
            </div>

            <li class="nav-item">
                <a class="nav-link" href="lihat_progress.php">
                    <i class="fas fa-fw fa-chart-line"></i>
                    <span>Lihat Progress</span></a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="file_management.php">
                    <i class="fas fa-fw fa-folder"></i>
                    <span>File Management</span></a>
            </li>

            <li class="nav-item active">
                <a class="nav-link" href="upload_brief.php">
                    <i class="fas fa-fw fa-cloud-upload-alt"></i>
                    <span>Upload Brief & Referensi</span></a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="ajukan_revisi.php">
                    <i class="fas fa-fw fa-edit"></i>
                    <span>Ajukan Revisi</span></a>
            </li>

            <!-- Divider -->
            <hr class="sidebar-divider d-none d-md-block">

            <li class="nav-item">
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Keluar</span></a>
            </li>

            <!-- Sidebar Toggler (Sidebar) -->
            <div class="text-center d-none d-md-inline">
                <button class="rounded-circle border-0" id="sidebarToggle"></button>
            </div>

        </ul>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">
                    <!-- Sidebar Toggle (Topbar) -->
                    <button id="sidebarToggleTop" class="btn btn-link d-md-none rounded-circle mr-3">
                        <i class="fa fa-bars"></i>
                    </button>

                    <!-- Topbar Navbar -->
                    <ul class="navbar-nav ml-auto">
                        <li class="nav-item dropdown no-arrow">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <span class="mr-2 d-none d-lg-inline text-gray-600 small"><?php echo $_SESSION['nama']; ?></span>
                                <img class="img-profile rounded-circle" src="../tmp/img/undraw_profile.svg">
                            </a>
                        </li>
                    </ul>
                </nav>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">

                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">
                            <i class="fas fa-cloud-upload-alt mr-2"></i>Upload Brief & Referensi
                        </h1>
                    </div>

                    <!-- Upload Form -->
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">
                                        <i class="fas fa-upload mr-2"></i>Upload Dokumen Baru
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <form action="proses_upload_brief.php" method="post" enctype="multipart/form-data" id="uploadForm">
                                        
                                        <div class="form-group">
                                            <label for="kategori">Kategori Dokumen</label>
                                            <select name="kategori" class="form-control" required>
                                                <option value="">Pilih Kategori</option>
                                                <option value="brief">Brief Proyek</option>
                                                <option value="referensi">Referensi Desain</option>
                                                <option value="dokumen">Dokumen Pendukung</option>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label for="deskripsi">Deskripsi Dokumen</label>
                                            <textarea name="deskripsi" class="form-control" rows="3" 
                                                placeholder="Jelaskan dokumen yang akan diupload..." maxlength="500" required></textarea>
                                            <small class="form-text text-muted">Maksimal 500 karakter</small>
                                        </div>

                                        <div class="form-group">
                                            <label>File Dokumen</label>
                                            <div class="upload-area" id="uploadArea">
                                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                                <h5>Drag & Drop file di sini</h5>
                                                <p class="text-muted">atau klik untuk memilih file</p>
                                                <input type="file" name="file_dokumen" id="fileInput" style="display: none;" required>
                                                <small class="text-muted">
                                                    Format yang didukung: PDF, DOC, DOCX, JPG, PNG, GIF (Maksimal 25MB)
                                                </small>
                                            </div>
                                            <div id="filePreview" class="mt-3"></div>
                                        </div>

                                        <div class="form-group">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-upload mr-2"></i>Upload Dokumen
                                            </button>
                                            <button type="reset" class="btn btn-secondary ml-2">
                                                <i class="fas fa-undo mr-2"></i>Reset
                                            </button>
                                        </div>

                                    </form>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-info">
                                        <i class="fas fa-info-circle mr-2"></i>Panduan Upload
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <h6 class="font-weight-bold">Brief Proyek:</h6>
                                    <p class="small text-muted">Dokumen yang berisi kebutuhan, spesifikasi, dan detail proyek yang diinginkan.</p>
                                    
                                    <h6 class="font-weight-bold">Referensi Desain:</h6>
                                    <p class="small text-muted">Gambar atau dokumen yang menjadi inspirasi atau contoh desain yang diinginkan.</p>
                                    
                                    <h6 class="font-weight-bold">Dokumen Pendukung:</h6>
                                    <p class="small text-muted">File tambahan seperti denah, ukuran, atau dokumen lain yang mendukung proyek.</p>
                                    
                                    <hr>
                                    <h6 class="font-weight-bold text-warning">Catatan Penting:</h6>
                                    <ul class="small text-muted">
                                        <li>File maksimal 25MB</li>
                                        <li>Gunakan nama file yang jelas</li>
                                        <li>Pastikan file tidak corrupt</li>
                                        <li>File akan dapat diakses oleh tim proyek</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <footer class="sticky-footer bg-white">
                <div class="container my-auto">
                    <div class="copyright text-center my-auto">
                        <span>Copyright &copy; Antosa Arsitek 2025</span>
                    </div>
                </div>
            </footer>
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Bootstrap core JavaScript-->
    <script src="../tmp/vendor/jquery/jquery.min.js"></script>
    <script src="../tmp/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>

    <!-- Core plugin JavaScript-->
    <script src="../tmp/vendor/jquery-easing/jquery.easing.min.js"></script>

    <!-- Custom scripts for all pages-->
    <script src="../tmp/js/sb-admin-2.min.js"></script>

    <script>
        $(document).ready(function() {
            const uploadArea = $('#uploadArea');
            const fileInput = $('#fileInput');
            const filePreview = $('#filePreview');

            // Click to select file
            uploadArea.on('click', function() {
                fileInput.click();
            });

            // Drag and drop functionality
            uploadArea.on('dragover', function(e) {
                e.preventDefault();
                $(this).addClass('dragover');
            });

            uploadArea.on('dragleave', function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');
            });

            uploadArea.on('drop', function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');
                
                const files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                    fileInput[0].files = files;
                    handleFileSelect(files[0]);
                }
            });

            // File input change
            fileInput.on('change', function() {
                if (this.files.length > 0) {
                    handleFileSelect(this.files[0]);
                }
            });

            function handleFileSelect(file) {
                const maxSize = 25 * 1024 * 1024; // 25MB
                const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/png', 'image/gif'];
                
                if (file.size > maxSize) {
                    alert('File terlalu besar! Maksimal 25MB.');
                    return;
                }

                if (!allowedTypes.includes(file.type)) {
                    alert('Format file tidak didukung!');
                    return;
                }

                // Show file preview
                let preview = `
                    <div class="file-item">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-file fa-2x text-primary mr-3"></i>
                            <div>
                                <h6 class="mb-1">${file.name}</h6>
                                <small class="text-muted">${formatFileSize(file.size)}</small>
                            </div>
                        </div>
                    </div>
                `;

                filePreview.html(preview);
            }

            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // Form reset
            $('#uploadForm')[0].addEventListener('reset', function() {
                filePreview.html('');
            });
        });
    </script>

</body>
</html>
