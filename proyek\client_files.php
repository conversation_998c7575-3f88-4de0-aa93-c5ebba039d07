<?php
session_start();

if (!isset($_SESSION['nama'])) {
    header("Location: ../index.php");
    exit;
}

if ($_SESSION['level'] != "proyek") {
    echo "<script>alert('Anda tidak memiliki akses ke halaman ini!'); window.location='../index.php';</script>";
    exit;
}

require '../koneksi.php';

// Get client uploaded files with category filter
$kategori_filter = $_GET['kategori'] ?? '';
$client_filter = $_GET['client'] ?? '';
$date_filter = $_GET['date'] ?? '';

// Base query for client uploaded files
$query = "SELECT fg.*, p.nama_petugas as uploader_name, p.level as uploader_level
          FROM file_gambar fg
          LEFT JOIN petugas p ON fg.uploaded_by = p.id_petugas
          WHERE p.level = 'client'";

// Add filters
$params = [];
$types = "";

if (!empty($kategori_filter)) {
    $query .= " AND fg.kategori_file = ?";
    $params[] = $kategori_filter;
    $types .= "s";
}

if (!empty($client_filter)) {
    $query .= " AND fg.uploaded_by = ?";
    $params[] = $client_filter;
    $types .= "i";
}

if (!empty($date_filter)) {
    $query .= " AND DATE(fg.created_at) = ?";
    $params[] = $date_filter;
    $types .= "s";
}

$query .= " ORDER BY fg.created_at DESC";

$stmt = mysqli_prepare($koneksi, $query);
if (!empty($params)) {
    mysqli_stmt_bind_param($stmt, $types, ...$params);
}
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

// Get list of clients for filter
$clients_query = "SELECT id_petugas, nama_petugas FROM petugas WHERE level = 'client' ORDER BY nama_petugas";
$clients_result = mysqli_query($koneksi, $clients_query);

// Get statistics
$stats_query = "SELECT 
    COUNT(*) as total_files,
    COUNT(CASE WHEN fg.kategori_file = 'brief' THEN 1 END) as brief_count,
    COUNT(CASE WHEN fg.kategori_file = 'referensi' THEN 1 END) as referensi_count,
    COUNT(CASE WHEN fg.kategori_file = 'dokumen' THEN 1 END) as dokumen_count,
    SUM(fg.file_size) as total_size
    FROM file_gambar fg
    LEFT JOIN petugas p ON fg.uploaded_by = p.id_petugas
    WHERE p.level = 'client'";
$stats_result = mysqli_query($koneksi, $stats_query);
$stats = mysqli_fetch_assoc($stats_result);
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>File Client - Tim Proyek</title>

    <!-- Custom fonts for this template-->
    <link href="../tmp/vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">
    <link href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i" rel="stylesheet">

    <!-- Custom styles for this template-->
    <link href="../tmp/css/sb-admin-2.min.css" rel="stylesheet">

    <style>
        .file-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            margin-right: 15px;
        }
        .file-item {
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }
        .file-item:hover {
            border-left-color: #4e73df;
            background-color: #f8f9fc;
        }
        .category-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        .stats-card {
            border-left: 4px solid #4e73df;
        }
    </style>
</head>

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">

            <!-- Sidebar - Brand -->
            <a class="sidebar-brand d-flex align-items-center justify-content-center" href="proyek.php">
                <div class="sidebar-brand-icon rotate-n-15">
                    <i class="fas fa-keyboard"></i>
                </div>
                <div class="sidebar-brand-text mx-3">Antosa</div>
            </a>

            <!-- Divider -->
            <hr class="sidebar-divider my-0">

            <!-- Nav Item - Dashboard -->
            <li class="nav-item">
                <a class="nav-link" href="proyek.php">
                    <i class="fas fa-fw fa-tachometer-alt"></i>
                    <span>Dashboard</span></a>
            </li>

            <!-- Divider -->
            <hr class="sidebar-divider">

            <!-- Heading -->
            <div class="sidebar-heading">
                Interface
            </div>

            <li class="nav-item">
                <a class="nav-link" href="tugas_harian.php">
                    <i class="fas fa-fw fa-tasks"></i>
                    <span>Tugas harian</span></a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="uploud_file.php">
                    <i class="fas fa-fw fa-upload"></i>
                    <span>Upload file desain</span></a>
            </li>

            <li class="nav-item active">
                <a class="nav-link" href="client_files.php">
                    <i class="fas fa-fw fa-folder-open"></i>
                    <span>File dari Client</span></a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="verifikasi.php">
                    <i class="fas fa-fw fa-check-circle"></i>
                    <span>Verifikasi</span></a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="input_tugas.php">
                    <i class="fas fa-fw fa-plus"></i>
                    <span>input tugas harian</span></a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="kelola_revisi.php">
                    <i class="fas fa-fw fa-edit"></i>
                    <span>Kelola Revisi</span></a>
            </li>

            <!-- Divider -->
            <hr class="sidebar-divider d-none d-md-block">

            <li class="nav-item">
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Keluar</span></a>
            </li>

            <!-- Sidebar Toggler (Sidebar) -->
            <div class="text-center d-none d-md-inline">
                <button class="rounded-circle border-0" id="sidebarToggle"></button>
            </div>

        </ul>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">
                    <!-- Sidebar Toggle (Topbar) -->
                    <button id="sidebarToggleTop" class="btn btn-link d-md-none rounded-circle mr-3">
                        <i class="fa fa-bars"></i>
                    </button>

                    <!-- Topbar Navbar -->
                    <ul class="navbar-nav ml-auto">
                        <li class="nav-item dropdown no-arrow">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <span class="mr-2 d-none d-lg-inline text-gray-600 small"><?php echo $_SESSION['nama']; ?></span>
                                <img class="img-profile rounded-circle" src="../tmp/img/undraw_profile.svg">
                            </a>
                        </li>
                    </ul>
                </nav>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">

                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">
                            <i class="fas fa-folder-open mr-2"></i>File dari Client
                        </h1>
                    </div>

                    <!-- Statistics Row -->
                    <div class="row mb-4">
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card stats-card shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total File</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['total_files']; ?></div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-file fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-success shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Brief Proyek</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['brief_count']; ?></div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-file-alt fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-info shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Referensi</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['referensi_count']; ?></div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-images fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-warning shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Total Size</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo formatFileSize($stats['total_size'] ?? 0); ?></div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-hdd fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-filter mr-2"></i>Filter File
                            </h6>
                        </div>
                        <div class="card-body">
                            <form method="GET" class="row">
                                <div class="col-md-3">
                                    <label for="kategori">Kategori</label>
                                    <select name="kategori" class="form-control">
                                        <option value="">Semua Kategori</option>
                                        <option value="brief" <?php echo $kategori_filter == 'brief' ? 'selected' : ''; ?>>Brief Proyek</option>
                                        <option value="referensi" <?php echo $kategori_filter == 'referensi' ? 'selected' : ''; ?>>Referensi Desain</option>
                                        <option value="dokumen" <?php echo $kategori_filter == 'dokumen' ? 'selected' : ''; ?>>Dokumen Pendukung</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="client">Client</label>
                                    <select name="client" class="form-control">
                                        <option value="">Semua Client</option>
                                        <?php while ($client = mysqli_fetch_assoc($clients_result)): ?>
                                            <option value="<?php echo $client['id_petugas']; ?>" 
                                                <?php echo $client_filter == $client['id_petugas'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($client['nama_petugas']); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="date">Tanggal Upload</label>
                                    <input type="date" name="date" class="form-control" value="<?php echo $date_filter; ?>">
                                </div>
                                <div class="col-md-3">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search mr-1"></i>Filter
                                        </button>
                                        <a href="client_files.php" class="btn btn-secondary">
                                            <i class="fas fa-undo mr-1"></i>Reset
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Files List -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-list mr-2"></i>Daftar File dari Client
                            </h6>
                        </div>
                        <div class="card-body">
                            <?php if (mysqli_num_rows($result) > 0): ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="bg-light">
                                            <tr>
                                                <th width="60">Type</th>
                                                <th>Deskripsi</th>
                                                <th width="120">Kategori</th>
                                                <th width="150">Client</th>
                                                <th width="120">Tanggal</th>
                                                <th width="100">Ukuran</th>
                                                <th width="150">Aksi</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php while ($file = mysqli_fetch_assoc($result)): ?>
                                                <?php
                                                $file_ext = strtolower(pathinfo($file['gambar'], PATHINFO_EXTENSION));
                                                $file_url = "../file_handler.php?id=" . $file['id'] . "&action=view";
                                                $download_url = "../file_handler.php?id=" . $file['id'] . "&action=download";

                                                // Determine file icon and color
                                                $icon_class = 'fas fa-file';
                                                $icon_color = 'text-secondary';

                                                switch ($file_ext) {
                                                    case 'pdf':
                                                        $icon_class = 'fas fa-file-pdf';
                                                        $icon_color = 'text-danger';
                                                        break;
                                                    case 'doc':
                                                    case 'docx':
                                                        $icon_class = 'fas fa-file-word';
                                                        $icon_color = 'text-primary';
                                                        break;
                                                    case 'jpg':
                                                    case 'jpeg':
                                                    case 'png':
                                                    case 'gif':
                                                        $icon_class = 'fas fa-file-image';
                                                        $icon_color = 'text-success';
                                                        break;
                                                }

                                                // Category badge color
                                                $badge_color = 'secondary';
                                                switch ($file['kategori_file']) {
                                                    case 'brief':
                                                        $badge_color = 'success';
                                                        break;
                                                    case 'referensi':
                                                        $badge_color = 'info';
                                                        break;
                                                    case 'dokumen':
                                                        $badge_color = 'warning';
                                                        break;
                                                }
                                                ?>
                                                <tr class="file-item">
                                                    <td>
                                                        <i class="<?php echo $icon_class . ' ' . $icon_color; ?> fa-lg"></i>
                                                    </td>
                                                    <td>
                                                        <div class="font-weight-bold"><?php echo htmlspecialchars($file['deskripsi']); ?></div>
                                                        <small class="text-muted"><?php echo htmlspecialchars($file['gambar']); ?></small>
                                                    </td>
                                                    <td>
                                                        <span class="badge badge-<?php echo $badge_color; ?> category-badge">
                                                            <?php echo ucfirst($file['kategori_file'] ?? 'Lainnya'); ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="font-weight-bold"><?php echo htmlspecialchars($file['uploader_name']); ?></div>
                                                        <small class="text-muted">Client</small>
                                                    </td>
                                                    <td>
                                                        <div><?php echo date('d/m/Y', strtotime($file['created_at'])); ?></div>
                                                        <small class="text-muted"><?php echo date('H:i', strtotime($file['created_at'])); ?></small>
                                                    </td>
                                                    <td>
                                                        <?php echo formatFileSize($file['file_size'] ?? 0); ?>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <a href="<?php echo $file_url; ?>" target="_blank"
                                                               class="btn btn-info btn-sm" title="Lihat File">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                            <a href="<?php echo $download_url; ?>"
                                                               class="btn btn-success btn-sm" title="Download">
                                                                <i class="fas fa-download"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endwhile; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">Tidak ada file yang ditemukan</h5>
                                    <p class="text-muted">Belum ada file yang diupload oleh client atau sesuai dengan filter yang dipilih.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <footer class="sticky-footer bg-white">
                <div class="container my-auto">
                    <div class="copyright text-center my-auto">
                        <span>Copyright &copy; Antosa Arsitek 2025</span>
                    </div>
                </div>
            </footer>
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Bootstrap core JavaScript-->
    <script src="../tmp/vendor/jquery/jquery.min.js"></script>
    <script src="../tmp/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>

    <!-- Core plugin JavaScript-->
    <script src="../tmp/vendor/jquery-easing/jquery.easing.min.js"></script>

    <!-- Custom scripts for all pages-->
    <script src="../tmp/js/sb-admin-2.min.js"></script>

</body>
</html>

<?php
function formatFileSize($bytes) {
    if ($bytes == 0) return '0 Bytes';
    $k = 1024;
    $sizes = array('Bytes', 'KB', 'MB', 'GB');
    $i = floor(log($bytes) / log($k));
    return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
}
?>
