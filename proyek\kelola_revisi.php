<?php
session_start();

if (!isset($_SESSION['nama'])) {
    header("Location: ../index.php");
    exit;
}

if ($_SESSION['level'] != "proyek") {
    echo "<script>alert('Anda tidak memiliki akses ke halaman ini!'); window.location='../index.php';</script>";
    exit;
}

require '../koneksi.php';

// Filter status
$status_filter = isset($_GET['status']) ? $_GET['status'] : 'all';
$where_clause = "";
if ($status_filter != 'all') {
    $where_clause = "WHERE rr.status_revisi = '" . mysqli_real_escape_string($koneksi, $status_filter) . "'";
}

// Ambil data revisi dengan informasi lengkap
$query_revisi = "SELECT rr.*, tp.nama_kegiatan, tp.status as tugas_status, 
                        pc.nama_petugas as client_name, ph.nama_petugas as handler_name
                 FROM revision_requests rr 
                 LEFT JOIN tugas_proyek tp ON rr.tugas_id = tp.id 
                 LEFT JOIN petugas pc ON rr.client_id = pc.id_petugas 
                 LEFT JOIN petugas ph ON rr.handled_by = ph.id_petugas 
                 $where_clause
                 ORDER BY 
                    CASE rr.prioritas 
                        WHEN 'urgent' THEN 1 
                        WHEN 'tinggi' THEN 2 
                        WHEN 'sedang' THEN 3 
                        WHEN 'rendah' THEN 4 
                    END,
                    rr.created_at DESC";
$result_revisi = mysqli_query($koneksi, $query_revisi);

// Hitung statistik
$stats_query = "SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status_revisi = 'pending' THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN status_revisi = 'diterima' THEN 1 ELSE 0 END) as diterima,
                    SUM(CASE WHEN status_revisi = 'ditolak' THEN 1 ELSE 0 END) as ditolak,
                    SUM(CASE WHEN status_revisi = 'selesai' THEN 1 ELSE 0 END) as selesai
                FROM revision_requests";
$stats_result = mysqli_query($koneksi, $stats_query);
$stats = mysqli_fetch_array($stats_result);

// Handle messages
$success_message = isset($_SESSION['success_message']) ? $_SESSION['success_message'] : '';
$error_message = isset($_SESSION['error_message']) ? $_SESSION['error_message'] : '';

// Clear session messages
unset($_SESSION['success_message']);
unset($_SESSION['error_message']);
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>Kelola Revisi - Project Dashboard</title>

    <!-- Custom fonts for this template-->
    <link href="../tmp/vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">
    <link href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i" rel="stylesheet">

    <!-- Custom styles for this template-->
    <link href="../tmp/css/sb-admin-2.min.css" rel="stylesheet">
</head>

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">

            <!-- Sidebar - Brand -->
            <a class="sidebar-brand d-flex align-items-center justify-content-center" href="proyek.php">
                <div class="sidebar-brand-icon rotate-n-15">
                    <i class="fas fa-keyboard"></i>
                </div>
                <div class="sidebar-brand-text mx-3">antosa</div>
            </a>

            <!-- Divider -->
            <hr class="sidebar-divider my-0">

            <!-- Nav Item - Dashboard -->
            <li class="nav-item">
                <a class="nav-link" href="proyek.php">
                    <i class="fas fa-fw fa-tachometer-alt"></i>
                    <span>Dashboard</span></a>         
            </li>
          
            <!-- Divider -->
            <hr class="sidebar-divider">

            <!-- Heading -->
            <div class="sidebar-heading">
                Interface
            </div>
            
             <li class="nav-item">
                <a class="nav-link" href="tugas_harian.php">
                    <i class="fas fa-fw fa-tasks"></i>
                    <span>Tugas harian</span></a>
            </li>

             <li class="nav-item">
                <a class="nav-link" href="uploud_file.php">
                    <i class="fas fa-fw fa-upload"></i>
                    <span>Upload file desain</span></a>
            </li>

             <li class="nav-item">
                <a class="nav-link" href="client_files.php">
                    <i class="fas fa-fw fa-folder-open"></i>
                    <span>File dari Client</span></a>
            </li>

             <li class="nav-item">
                <a class="nav-link" href="verifikasi.php">
                    <i class="fas fa-fw fa-check-circle"></i>
                    <span>Verifikasi</span></a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="input_tugas.php">
                    <i class="fas fa-fw fa-plus"></i>
                    <span>Input tugas harian</span></a>    
            </li>

            <li class="nav-item active">
                <a class="nav-link" href="kelola_revisi.php">
                    <i class="fas fa-fw fa-edit"></i>
                    <span>Kelola Revisi</span></a>    
            </li>

            <!-- Divider -->
            <hr class="sidebar-divider d-none d-md-block">

             <li class="nav-item">
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Keluar</span></a>
            </li>
            
            <!-- Sidebar Toggler (Sidebar) -->
            <div class="text-center d-none d-md-inline">
                <button class="rounded-circle border-0" id="sidebarToggle"></button>
            </div>

        </ul>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">

                    <!-- Sidebar Toggle (Topbar) -->
                    <button id="sidebarToggleTop" class="btn btn-link d-md-none rounded-circle mr-3">
                        <i class="fa fa-bars"></i>
                    </button>

                    <h1>Kelola Revisi</h1>

                </nav>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">

                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">Kelola Permintaan Revisi</h1>
                    </div>

                    <!-- Messages -->
                    <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <?php endif; ?>

                    <!-- Statistics Cards -->
                    <div class="row">
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-primary shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Revisi</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['total']; ?></div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-edit fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-warning shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Pending</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['pending']; ?></div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-success shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Diterima</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['diterima']; ?></div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-check fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-info shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Selesai</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['selesai']; ?></div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-flag-checkered fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filter and Table -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">Daftar Permintaan Revisi</h6>
                            <div class="dropdown no-arrow">
                                <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink"
                                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="fas fa-filter fa-sm fa-fw text-gray-400"></i>
                                </a>
                                <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in"
                                    aria-labelledby="dropdownMenuLink">
                                    <div class="dropdown-header">Filter Status:</div>
                                    <a class="dropdown-item <?php echo $status_filter == 'all' ? 'active' : ''; ?>" href="?status=all">Semua</a>
                                    <a class="dropdown-item <?php echo $status_filter == 'pending' ? 'active' : ''; ?>" href="?status=pending">Pending</a>
                                    <a class="dropdown-item <?php echo $status_filter == 'diterima' ? 'active' : ''; ?>" href="?status=diterima">Diterima</a>
                                    <a class="dropdown-item <?php echo $status_filter == 'ditolak' ? 'active' : ''; ?>" href="?status=ditolak">Ditolak</a>
                                    <a class="dropdown-item <?php echo $status_filter == 'selesai' ? 'active' : ''; ?>" href="?status=selesai">Selesai</a>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>No</th>
                                            <th>Judul Revisi</th>
                                            <th>Client</th>
                                            <th>Tugas</th>
                                            <th>Prioritas</th>
                                            <th>Status</th>
                                            <th>Tanggal</th>
                                            <th>Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $no = 1;
                                        if (mysqli_num_rows($result_revisi) > 0):
                                            while($revisi = mysqli_fetch_array($result_revisi)):
                                        ?>
                                        <tr>
                                            <td><?php echo $no++; ?></td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($revisi['judul_revisi']); ?></strong>
                                                <br>
                                                <small class="text-muted">
                                                    <?php echo htmlspecialchars(substr($revisi['deskripsi_revisi'], 0, 100)); ?>
                                                    <?php if (strlen($revisi['deskripsi_revisi']) > 100) echo '...'; ?>
                                                </small>
                                            </td>
                                            <td><?php echo htmlspecialchars($revisi['client_name']); ?></td>
                                            <td>
                                                <?php echo htmlspecialchars($revisi['nama_kegiatan']); ?>
                                                <br>
                                                <small class="text-muted">(<?php echo ucfirst($revisi['tugas_status']); ?>)</small>
                                            </td>
                                            <td>
                                                <span class="badge badge-<?php
                                                    echo $revisi['prioritas'] == 'urgent' ? 'danger' :
                                                        ($revisi['prioritas'] == 'tinggi' ? 'warning' :
                                                        ($revisi['prioritas'] == 'sedang' ? 'info' : 'secondary'));
                                                ?>">
                                                    <?php echo ucfirst($revisi['prioritas']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge badge-<?php
                                                    echo $revisi['status_revisi'] == 'pending' ? 'warning' :
                                                        ($revisi['status_revisi'] == 'diterima' ? 'success' :
                                                        ($revisi['status_revisi'] == 'ditolak' ? 'danger' : 'info'));
                                                ?>">
                                                    <?php echo ucfirst($revisi['status_revisi']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo date('d M Y', strtotime($revisi['created_at'])); ?></td>
                                            <td>
                                                <button class="btn btn-info btn-sm" data-toggle="modal"
                                                        data-target="#detailModal<?php echo $revisi['id']; ?>">
                                                    <i class="fas fa-eye"></i> Detail
                                                </button>
                                                <?php if ($revisi['status_revisi'] == 'pending'): ?>
                                                <button class="btn btn-success btn-sm" data-toggle="modal"
                                                        data-target="#responseModal<?php echo $revisi['id']; ?>">
                                                    <i class="fas fa-reply"></i> Respon
                                                </button>
                                                <?php endif; ?>
                                            </td>
                                        </tr>

                                        <!-- Detail Modal -->
                                        <div class="modal fade" id="detailModal<?php echo $revisi['id']; ?>" tabindex="-1" role="dialog">
                                            <div class="modal-dialog modal-lg" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title">Detail Revisi: <?php echo htmlspecialchars($revisi['judul_revisi']); ?></h5>
                                                        <button type="button" class="close" data-dismiss="modal">
                                                            <span>&times;</span>
                                                        </button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <div class="row">
                                                            <div class="col-md-6">
                                                                <strong>Client:</strong> <?php echo htmlspecialchars($revisi['client_name']); ?><br>
                                                                <strong>Tugas:</strong> <?php echo htmlspecialchars($revisi['nama_kegiatan']); ?><br>
                                                                <strong>Prioritas:</strong>
                                                                <span class="badge badge-<?php
                                                                    echo $revisi['prioritas'] == 'urgent' ? 'danger' :
                                                                        ($revisi['prioritas'] == 'tinggi' ? 'warning' :
                                                                        ($revisi['prioritas'] == 'sedang' ? 'info' : 'secondary'));
                                                                ?>">
                                                                    <?php echo ucfirst($revisi['prioritas']); ?>
                                                                </span><br>
                                                                <strong>Status:</strong>
                                                                <span class="badge badge-<?php
                                                                    echo $revisi['status_revisi'] == 'pending' ? 'warning' :
                                                                        ($revisi['status_revisi'] == 'diterima' ? 'success' :
                                                                        ($revisi['status_revisi'] == 'ditolak' ? 'danger' : 'info'));
                                                                ?>">
                                                                    <?php echo ucfirst($revisi['status_revisi']); ?>
                                                                </span>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <strong>Tanggal Request:</strong> <?php echo date('d M Y H:i', strtotime($revisi['created_at'])); ?><br>
                                                                <?php if ($revisi['tanggal_response']): ?>
                                                                <strong>Tanggal Response:</strong> <?php echo date('d M Y H:i', strtotime($revisi['tanggal_response'])); ?><br>
                                                                <?php endif; ?>
                                                                <?php if ($revisi['handler_name']): ?>
                                                                <strong>Ditangani oleh:</strong> <?php echo htmlspecialchars($revisi['handler_name']); ?><br>
                                                                <?php endif; ?>
                                                                <?php if ($revisi['file_pendukung']): ?>
                                                                <strong>File Pendukung:</strong>
                                                                <a href="../uploads/revisi/<?php echo $revisi['file_pendukung']; ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                                                    <i class="fas fa-download"></i> Download
                                                                </a>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                        <hr>
                                                        <strong>Deskripsi Revisi:</strong>
                                                        <p><?php echo nl2br(htmlspecialchars($revisi['deskripsi_revisi'])); ?></p>

                                                        <?php if ($revisi['response_notes']): ?>
                                                        <hr>
                                                        <strong>Catatan Response:</strong>
                                                        <p><?php echo nl2br(htmlspecialchars($revisi['response_notes'])); ?></p>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Response Modal -->
                                        <?php if ($revisi['status_revisi'] == 'pending'): ?>
                                        <div class="modal fade" id="responseModal<?php echo $revisi['id']; ?>" tabindex="-1" role="dialog">
                                            <div class="modal-dialog" role="document">
                                                <div class="modal-content">
                                                    <form action="proses_revisi.php" method="POST">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title">Respon Revisi</h5>
                                                            <button type="button" class="close" data-dismiss="modal">
                                                                <span>&times;</span>
                                                            </button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <input type="hidden" name="revisi_id" value="<?php echo $revisi['id']; ?>">

                                                            <div class="form-group">
                                                                <label>Status Response</label>
                                                                <select class="form-control" name="status_revisi" required>
                                                                    <option value="">-- Pilih Status --</option>
                                                                    <option value="diterima">Terima Revisi</option>
                                                                    <option value="ditolak">Tolak Revisi</option>
                                                                    <option value="selesai">Revisi Selesai</option>
                                                                </select>
                                                            </div>

                                                            <div class="form-group">
                                                                <label>Catatan Response</label>
                                                                <textarea class="form-control" name="response_notes" rows="4"
                                                                          placeholder="Berikan catatan atau penjelasan..."></textarea>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                                                            <button type="submit" class="btn btn-primary">Simpan Response</button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endif; ?>

                                        <?php
                                            endwhile;
                                        else:
                                        ?>
                                        <tr>
                                            <td colspan="8" class="text-center text-muted">Tidak ada data revisi.</td>
                                        </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <footer class="sticky-footer bg-white">
                <div class="container my-auto">
                    <div class="copyright text-center my-auto">
                        <span>Copyright &copy;FOKUS UKK!!</span>
                    </div>
                </div>
            </footer>
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Bootstrap core JavaScript -->
    <script src="../tmp/vendor/jquery/jquery.min.js"></script>
    <script src="../tmp/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>

    <!-- Core plugin JavaScript -->
    <script src="../tmp/vendor/jquery-easing/jquery.easing.min.js"></script>

    <!-- Custom scripts for all pages -->
    <script src="../tmp/js/sb-admin-2.min.js"></script>

</body>
</html>
