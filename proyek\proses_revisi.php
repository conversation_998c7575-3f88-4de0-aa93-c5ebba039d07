<?php
session_start();

if (!isset($_SESSION['nama'])) {
    header("Location: ../index.php");
    exit;
}

if ($_SESSION['level'] != "proyek") {
    echo "<script>alert('Anda tidak memiliki akses ke halaman ini!'); window.location='../index.php';</script>";
    exit;
}

require '../koneksi.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Ambil data dari form
    $revisi_id = mysqli_real_escape_string($koneksi, $_POST['revisi_id']);
    $status_revisi = mysqli_real_escape_string($koneksi, $_POST['status_revisi']);
    $response_notes = mysqli_real_escape_string($koneksi, $_POST['response_notes']);
    $handled_by = $_SESSION['id_petugas'];
    
    // Validasi input
    $errors = array();
    
    if (empty($revisi_id)) {
        $errors[] = "ID revisi tidak valid";
    }
    
    if (empty($status_revisi)) {
        $errors[] = "Status revisi harus dipilih";
    }
    
    if (!in_array($status_revisi, ['diterima', 'ditolak', 'selesai'])) {
        $errors[] = "Status revisi tidak valid";
    }
    
    // Validasi revisi exists dan masih pending
    $check_revisi = "SELECT id, status_revisi FROM revision_requests WHERE id = '$revisi_id' AND status_revisi = 'pending'";
    $result_check = mysqli_query($koneksi, $check_revisi);
    if (mysqli_num_rows($result_check) == 0) {
        $errors[] = "Revisi tidak ditemukan atau sudah diproses";
    }
    
    // Jika tidak ada error, update status revisi
    if (empty($errors)) {
        $current_time = date('Y-m-d H:i:s');
        
        $query = "UPDATE revision_requests 
                  SET status_revisi = '$status_revisi', 
                      response_notes = " . ($response_notes ? "'$response_notes'" : "NULL") . ",
                      handled_by = '$handled_by',
                      tanggal_response = '$current_time',
                      updated_at = '$current_time'
                  WHERE id = '$revisi_id'";
        
        if (mysqli_query($koneksi, $query)) {
            // Set success message berdasarkan status
            $status_message = '';
            switch ($status_revisi) {
                case 'diterima':
                    $status_message = 'Revisi telah diterima dan akan segera dikerjakan.';
                    break;
                case 'ditolak':
                    $status_message = 'Revisi telah ditolak dengan alasan yang telah diberikan.';
                    break;
                case 'selesai':
                    $status_message = 'Revisi telah selesai dikerjakan.';
                    break;
            }
            
            $_SESSION['success_message'] = "Response berhasil disimpan. " . $status_message;
            
            // Optional: Update related tugas_proyek status jika revisi diterima
            if ($status_revisi == 'diterima') {
                // Ambil tugas_id dari revisi
                $get_tugas = "SELECT tugas_id FROM revision_requests WHERE id = '$revisi_id'";
                $result_tugas = mysqli_query($koneksi, $get_tugas);
                if ($row_tugas = mysqli_fetch_array($result_tugas)) {
                    $tugas_id = $row_tugas['tugas_id'];
                    
                    // Update status tugas menjadi 'proses' untuk revisi
                    $update_tugas = "UPDATE tugas_proyek 
                                     SET status = 'proses', 
                                         progress_percentage = GREATEST(progress_percentage - 25, 0),
                                         updated_at = '$current_time'
                                     WHERE id = '$tugas_id'";
                    mysqli_query($koneksi, $update_tugas);
                }
            }
            
        } else {
            $errors[] = "Gagal menyimpan response: " . mysqli_error($koneksi);
        }
    }
    
    // Jika ada error, simpan ke session
    if (!empty($errors)) {
        $_SESSION['error_message'] = implode('<br>', $errors);
    }
    
    // Redirect kembali ke halaman kelola revisi
    header("Location: kelola_revisi.php");
    exit;
    
} else {
    // Jika bukan POST request, redirect ke halaman kelola revisi
    header("Location: kelola_revisi.php");
    exit;
}
?>
